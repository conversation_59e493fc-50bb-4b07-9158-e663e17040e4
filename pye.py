import pandas as pd
from pye import options as opts
from pye.charts import Map
from pye.charts import Bar3D
from pye.charts import Funnel
import numpy as np

# 读取 Excel 文件
df = pd.read_csv('销售明细表-gen.csv')

# 1.地图--统计各省份的数量
province_counts = df['省份'].value_counts().reset_index(name='数量')
data1 = [list(z) for z in zip(province_counts['省份'], province_counts['数量'])]
# 创建地图对象
map_chart = (
    Map()
    .add("数量", data1, "china")
    .set_global_opts(
        title_opts=opts.TitleOpts(title="各省份数量分布地图"),
        visualmap_opts=opts.VisualMapOpts(min_=province_counts['数量'].min(),
                                          max_=province_counts['数量'].max()),
        toolbox_opts=opts.ToolboxOpts(is_show=True)
    )
)

# 2.3D--统计不同商品类别的销售额总和
category_sales = df.groupby('商品类别')['销售额'].sum().reset_index()
data2 = []
for i, row in category_sales.iterrows():
    data2.append([i, 0, row['销售额'], row['销售额']])
# 创建 3D 柱状图对象
bar3d = (
    Bar3D(init_opts=opts.InitOpts(width="1600px", height="800px"))
    .add(
        series_name="销售额",
        data=data2,
        xaxis3d_opts=opts.Axis3DOpts(type_="category", data=category_sales['商品类别'].tolist()),
        yaxis3d_opts=opts.Axis3DOpts(type_="value"),
        zaxis3d_opts=opts.Axis3DOpts(type_="value"),
        grid3d_opts=opts.Grid3DOpts(width=100, height=40, depth=160),
    )
    .set_global_opts(
        title_opts=opts.TitleOpts(title="不同商品类别的销售额 3D 柱状图"),
        visualmap_opts=opts.VisualMapOpts(
            max_=np.max(category_sales['销售额']),
            range_color=["#313695", "#4575b4", "#74add1", "#abd9e9", "#e0f3f8", "#ffffbf",
                         "#fee090", "#fdae61", "#f46d43", "#d73027", "#a50026"],
        ),
        toolbox_opts=opts.ToolboxOpts(is_show=True)
    )
)

# 按商品类别统计销售额总和
category_sales = df.groupby('商品类别')['销售额'].sum().reset_index()
category_sales = category_sales.sort_values(by='销售额', ascending=False)
data3 = [list(z) for z in zip(category_sales['商品类别'], category_sales['销售额'])]
# 创建漏斗图对象
funnel = (
    Funnel()
    .add(
        series_name="销售额",
        data_pair=data3,
        gap=2,
        tooltip_opts=opts.TooltipOpts(is_show=True, trigger="item", formatter="{a} <br/>{b} : {c}"),
        label_opts=opts.LabelOpts(is_show=True, position="inside"),
        itemstyle_opts=opts.ItemStyleOpts(border_color="#fff", border_width=1),
    )
    .set_global_opts(
        title_opts=opts.TitleOpts(title="不同商品类别的销售额漏斗图"),
        toolbox_opts=opts.ToolboxOpts(is_show=True)
    )
)

# 渲染地图到 HTML 文件，并使用静态JavaScript
map_chart.render("static/templates/pycharts1.html")